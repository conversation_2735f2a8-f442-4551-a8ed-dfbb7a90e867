import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  DealerAssessmentAssignment,
  Action,
} from '../models';
import {DealerAssessmentAssignmentRepository} from '../repositories';

export class DealerAssessmentAssignmentActionController {
  constructor(
    @repository(DealerAssessmentAssignmentRepository) protected dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
  ) { }

  @get('/dealer-assessment-assignments/{id}/actions', {
    responses: {
      '200': {
        description: 'Array of DealerAssessmentAssignment has many Action',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Action)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.dealerAssessmentAssignmentRepository.actions(id).find(filter);
  }

  @post('/dealer-assessment-assignments/{id}/actions', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(Action)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof DealerAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewActionInDealerAssessmentAssignment',
            exclude: ['id'],
            optional: ['appId']
          }),
        },
      },
    }) action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.dealerAssessmentAssignmentRepository.actions(id).create(action);
  }

  @patch('/dealer-assessment-assignments/{id}/actions', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment.Action PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Partial<Action>,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.actions(id).patch(action, where);
  }

  @del('/dealer-assessment-assignments/{id}/actions', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment.Action DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.actions(id).delete(where);
  }
}
