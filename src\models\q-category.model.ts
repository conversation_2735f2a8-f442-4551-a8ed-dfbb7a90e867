import {Entity, model, property, hasMany} from '@loopback/repository';
import {QTopic} from './q-topic.model';

@model()
export class QCategory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @hasMany(() => QTopic)
  qTopics: QTopic[];

  constructor(data?: Partial<QCategory>) {
    super(data);
  }
}

export interface QCategoryRelations {
  // describe navigational properties here
}

export type QCategoryWithRelations = QCategory & QCategoryRelations;
