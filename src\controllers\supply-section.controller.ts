import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SupplySection} from '../models';
import {SupplySectionRepository} from '../repositories';

export class SupplySectionController {
  constructor(
    @repository(SupplySectionRepository)
    public supplySectionRepository : SupplySectionRepository,
  ) {}

  @post('/supply-sections')
  @response(200, {
    description: 'SupplySection model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplySection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplySection, {
            title: 'NewSupplySection',
            exclude: ['id'],
          }),
        },
      },
    })
    supplySection: Omit<SupplySection, 'id'>,
  ): Promise<SupplySection> {
    return this.supplySectionRepository.create(supplySection);
  }

  @get('/supply-sections/count')
  @response(200, {
    description: 'SupplySection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplySection) where?: Where<SupplySection>,
  ): Promise<Count> {
    return this.supplySectionRepository.count(where);
  }

  @get('/supply-sections')
  @response(200, {
    description: 'Array of SupplySection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplySection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplySection) filter?: Filter<SupplySection>,
  ): Promise<SupplySection[]> {
    return this.supplySectionRepository.find(filter);
  }

  @patch('/supply-sections')
  @response(200, {
    description: 'SupplySection PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplySection, {partial: true}),
        },
      },
    })
    supplySection: SupplySection,
    @param.where(SupplySection) where?: Where<SupplySection>,
  ): Promise<Count> {
    return this.supplySectionRepository.updateAll(supplySection, where);
  }

  @get('/supply-sections/{id}')
  @response(200, {
    description: 'SupplySection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplySection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplySection, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplySection>
  ): Promise<SupplySection> {
    return this.supplySectionRepository.findById(id, filter);
  }

  @patch('/supply-sections/{id}')
  @response(204, {
    description: 'SupplySection PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplySection, {partial: true}),
        },
      },
    })
    supplySection: SupplySection,
  ): Promise<void> {
    await this.supplySectionRepository.updateById(id, supplySection);
  }

  @put('/supply-sections/{id}')
  @response(204, {
    description: 'SupplySection PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplySection: SupplySection,
  ): Promise<void> {
    await this.supplySectionRepository.replaceById(id, supplySection);
  }

  @del('/supply-sections/{id}')
  @response(204, {
    description: 'SupplySection DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplySectionRepository.deleteById(id);
  }
}
