import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SupplyCategory,
  SupplySection,
} from '../models';
import {SupplyCategoryRepository} from '../repositories';

export class SupplyCategorySupplySectionController {
  constructor(
    @repository(SupplyCategoryRepository) protected supplyCategoryRepository: SupplyCategoryRepository,
  ) { }

  @get('/supply-categories/{id}/supply-sections', {
    responses: {
      '200': {
        description: 'Array of SupplyCategory has many SupplySection',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplySection)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SupplySection>,
  ): Promise<SupplySection[]> {
    return this.supplyCategoryRepository.supplySections(id).find(filter);
  }

  @post('/supply-categories/{id}/supply-sections', {
    responses: {
      '200': {
        description: 'SupplyCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplySection)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof SupplyCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplySection, {
            title: 'NewSupplySectionInSupplyCategory',
            exclude: ['id'],
            optional: ['supplyCategoryId']
          }),
        },
      },
    }) supplySection: Omit<SupplySection, 'id'>,
  ): Promise<SupplySection> {
    return this.supplyCategoryRepository.supplySections(id).create(supplySection);
  }

  @patch('/supply-categories/{id}/supply-sections', {
    responses: {
      '200': {
        description: 'SupplyCategory.SupplySection PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplySection, {partial: true}),
        },
      },
    })
    supplySection: Partial<SupplySection>,
    @param.query.object('where', getWhereSchemaFor(SupplySection)) where?: Where<SupplySection>,
  ): Promise<Count> {
    return this.supplyCategoryRepository.supplySections(id).patch(supplySection, where);
  }

  @del('/supply-categories/{id}/supply-sections', {
    responses: {
      '200': {
        description: 'SupplyCategory.SupplySection DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SupplySection)) where?: Where<SupplySection>,
  ): Promise<Count> {
    return this.supplyCategoryRepository.supplySections(id).delete(where);
  }
}
