import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SupplyChecklist} from '../models';
import {SupplyChecklistRepository} from '../repositories';

export class SupplyChecklistController {
  constructor(
    @repository(SupplyChecklistRepository)
    public supplyChecklistRepository : SupplyChecklistRepository,
  ) {}

  @post('/supply-checklists')
  @response(200, {
    description: 'SupplyChecklist model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplyChecklist)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyChecklist, {
            title: 'NewSupplyChecklist',
            exclude: ['id'],
          }),
        },
      },
    })
    supplyChecklist: Omit<SupplyChecklist, 'id'>,
  ): Promise<SupplyChecklist> {
    return this.supplyChecklistRepository.create(supplyChecklist);
  }

  @get('/supply-checklists/count')
  @response(200, {
    description: 'SupplyChecklist model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplyChecklist) where?: Where<SupplyChecklist>,
  ): Promise<Count> {
    return this.supplyChecklistRepository.count(where);
  }

  @get('/supply-checklists')
  @response(200, {
    description: 'Array of SupplyChecklist model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplyChecklist, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplyChecklist) filter?: Filter<SupplyChecklist>,
  ): Promise<SupplyChecklist[]> {
    return this.supplyChecklistRepository.find(filter);
  }

  @patch('/supply-checklists')
  @response(200, {
    description: 'SupplyChecklist PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyChecklist, {partial: true}),
        },
      },
    })
    supplyChecklist: SupplyChecklist,
    @param.where(SupplyChecklist) where?: Where<SupplyChecklist>,
  ): Promise<Count> {
    return this.supplyChecklistRepository.updateAll(supplyChecklist, where);
  }

  @get('/supply-checklists/{id}')
  @response(200, {
    description: 'SupplyChecklist model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplyChecklist, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplyChecklist, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplyChecklist>
  ): Promise<SupplyChecklist> {
    return this.supplyChecklistRepository.findById(id, filter);
  }

  @patch('/supply-checklists/{id}')
  @response(204, {
    description: 'SupplyChecklist PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyChecklist, {partial: true}),
        },
      },
    })
    supplyChecklist: SupplyChecklist,
  ): Promise<void> {
    await this.supplyChecklistRepository.updateById(id, supplyChecklist);
  }

  @put('/supply-checklists/{id}')
  @response(204, {
    description: 'SupplyChecklist PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplyChecklist: SupplyChecklist,
  ): Promise<void> {
    await this.supplyChecklistRepository.replaceById(id, supplyChecklist);
  }

  @del('/supply-checklists/{id}')
  @response(204, {
    description: 'SupplyChecklist DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplyChecklistRepository.deleteById(id);
  }
}
