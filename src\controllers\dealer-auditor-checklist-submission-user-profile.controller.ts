import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  DealerAuditorChecklistSubmission,
  UserProfile,
} from '../models';
import {DealerAuditorChecklistSubmissionRepository} from '../repositories';

export class DealerAuditorChecklistSubmissionUserProfileController {
  constructor(
    @repository(DealerAuditorChecklistSubmissionRepository)
    public dealerAuditorChecklistSubmissionRepository: DealerAuditorChecklistSubmissionRepository,
  ) { }

  @get('/dealer-auditor-checklist-submissions/{id}/user-profile', {
    responses: {
      '200': {
        description: 'UserProfile belonging to DealerAuditorChecklistSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(UserProfile),
          },
        },
      },
    },
  })
  async getUserProfile(
    @param.path.number('id') id: typeof DealerAuditorChecklistSubmission.prototype.id,
  ): Promise<UserProfile> {
    return this.dealerAuditorChecklistSubmissionRepository.dealer(id);
  }
}
