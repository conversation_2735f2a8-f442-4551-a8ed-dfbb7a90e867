import {Entity, model, property} from '@loopback/repository';

@model()
export class EmployeeData extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: false,
  })
  employeeId?: string; // Raw employee_type from file

  @property({
    type: 'string',
    required: true,
  })
  EmployeeRoleType?: string; // Converted: "Employee" or "Worker" based on contract_type

  @property({
    type: 'string',
    required: true,
  })
  EmployeeCategory?: string; // Converted: "Permanent" or "Other than Permanent" based on employee_type

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  contract_type?: string | null; // Raw contract_type from file

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  raw_grade?: string | null; // Raw grade from file

  @property({
    type: 'string',
    required: true,
  })
  EmployeeGender?: string;

  @property({
    type: 'string',
    required: true,
  })
  EmployeeAge?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeDOE?: string | null; // Date of Entry/Joining

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeDOJ?: string | null; // Date of Joining (alternative field)

  @property({
    type: 'string',
    required: false,
  })
  locationType?: string;

  @property({
    type: 'string',
    required: true,
  })
  officeCity?: string;

  @property({
    type: 'string',
    required: true,
  })
  officeLocation?: string;



  @property({
    type: 'string',
    required: true,
  })
  EmployeeGrade?: string; // Converted grade classification (Senior Management, Middle Management, Non-Management)

  @property({
    type: 'string',
    required: true,
    default: 'Active',
  })
  employeeStatus?: 'Active' | 'Inactive';

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  date_of_exit?: string | null; // Only for inactive employees

  @property({
    type: 'string',
    required: true,
  })
  syncDate?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'number',
  })
  locationId?: number;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier0_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier1_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier2_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier3_id?: number | null;

  constructor(data?: Partial<EmployeeData>) {
    super(data);
  }
}

export interface EmployeeDataRelations {
  // describe navigational properties here
}

export type EmployeeDataWithRelations = EmployeeData & EmployeeDataRelations;
