import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplyChecklist,
  UserProfile,
} from '../models';
import {SupplyChecklistRepository} from '../repositories';

export class SupplyChecklistUserProfileController {
  constructor(
    @repository(SupplyChecklistRepository)
    public supplyChecklistRepository: SupplyChecklistRepository,
  ) { }

  @get('/supply-checklists/{id}/user-profile', {
    responses: {
      '200': {
        description: 'UserProfile belonging to SupplyChecklist',
        content: {
          'application/json': {
            schema: getModelSchemaRef(UserProfile),
          },
        },
      },
    },
  })
  async getUserProfile(
    @param.path.number('id') id: typeof SupplyChecklist.prototype.id,
  ): Promise<UserProfile> {
    return this.supplyChecklistRepository.createdBy(id);
  }
}
