// Uncomment these imports to begin using these cool features!

import {inject} from '@loopback/core';
import {post, requestBody, Response, RestBindings} from '@loopback/rest';
import {
  AlignmentType,
  Document,
  HeadingLevel,
  Packer,
  PageBreak,
  Paragraph,
  TableOfContents,
} from 'docx';
import * as path from 'path';
import puppeteer from 'puppeteer';
import {generateDisclosure2_2} from '../SR_Utils/utils/disclosure-2/disclosure-2-2.generator';
import {generateDisclosure2_3} from '../SR_Utils/utils/disclosure-2/disclosure-2-3.generator';
import {generateDisclosure2_4} from '../SR_Utils/utils/disclosure-2/disclosure-2-4.generator';
import {generateDisclosure2_5} from '../SR_Utils/utils/disclosure-2/disclosure-2-5.generator';
import {generateDisclosure2_6} from '../SR_Utils/utils/disclosure-2/disclosure-2-6.generator';
import {generateDisclosure2_7} from '../SR_Utils/utils/disclosure-2/disclosure-2-7.generator';
import {generateDisclosure2_8} from '../SR_Utils/utils/disclosure-2/disclosure-2-8.generator';
import {generateDisclosureAirEmissions} from '../SR_Utils/utils/disclosure-2/disclosure-air-emissions.generator';
import {generateDisclosureAnnualTotalCompensationRatio} from '../SR_Utils/utils/disclosure-2/disclosure-annual-total-compensation-ratio.generator';
import {generateDisclosureAntiCompetitiveBehavior} from '../SR_Utils/utils/disclosure-2/disclosure-anti-competitive-behavior.generator';
import {generateDisclosureAntiCorruption} from '../SR_Utils/utils/disclosure-2/disclosure-anti-corruption.generator';
import {generateDisclosureBasicSalaryRemuneration} from '../SR_Utils/utils/disclosure-2/disclosure-basic-salary.generator';
import {generateDisclosureBenefitsProvided} from '../SR_Utils/utils/disclosure-2/disclosure-benefits-provided.generator';
import {generateDisclosureBiodiversity} from '../SR_Utils/utils/disclosure-2/disclosure-biodiversity.generator';
import {generateDisclosureCarbonPricing} from '../SR_Utils/utils/disclosure-2/disclosure-carbon-pricing.generator';
import {generateDisclosureCareerDevelopment} from '../SR_Utils/utils/disclosure-2/disclosure-career-devt-program.generator';
import {generateDisclosureChildLabor} from '../SR_Utils/utils/disclosure-2/disclosure-child-labour.generator';
import {generateDisclosureCollectiveBargainingAgreements} from '../SR_Utils/utils/disclosure-2/disclosure-collective-bargaining-agreements.generator';
import {generateDisclosureComplianceLawsRegulations} from '../SR_Utils/utils/disclosure-2/disclosure-compliance-laws-regulations.generator';
import {generateDisclosureConflictsInterest} from '../SR_Utils/utils/disclosure-2/disclosure-conflicts-of-interest.generator';
import {generateDisclosureDataPrivacy} from '../SR_Utils/utils/disclosure-2/disclosure-cybersecurity.generator';
import {generateDisclosureDiversityInclusion} from '../SR_Utils/utils/disclosure-2/disclosure-diversity-inclusion.generator';
import {generateDisclosureEconomicPerformance} from '../SR_Utils/utils/disclosure-2/disclosure-economic-performance.generator';
import {generateDisclosureGHGIntensity} from '../SR_Utils/utils/disclosure-2/disclosure-emissions-intensity.generator';
import {generateDisclosureODS} from '../SR_Utils/utils/disclosure-2/disclosure-emissions-ods.generator';
import {generateDisclosureGHGReduction} from '../SR_Utils/utils/disclosure-2/disclosure-emissions-reduction.generator';
import {generateDisclosureEmissionsGHG} from '../SR_Utils/utils/disclosure-2/disclosure-emissions.generator';
import {generateDisclosureEnergyOutside} from '../SR_Utils/utils/disclosure-2/disclosure-energy-consumption-outside-organization.generator';
import {generateDisclosureEnergyReductionProducts} from '../SR_Utils/utils/disclosure-2/disclosure-energy-intensity-products-services.generator';
import {generateDisclosureEnergyIntensity} from '../SR_Utils/utils/disclosure-2/disclosure-energy-intensity.generator';
import {generateDisclosureEnergyReduction} from '../SR_Utils/utils/disclosure-2/disclosure-energy-reduction.generator';
import {generateDisclosureEnergy} from '../SR_Utils/utils/disclosure-2/disclosure-energy.generator';
import {generateDisclosureForcedLabour} from '../SR_Utils/utils/disclosure-2/disclosure-forced-labour.generator';
import {generateDisclosureGovernanceStructureComposition} from '../SR_Utils/utils/disclosure-2/disclosure-governance-structure-composition.generator';
import {generateDisclosureHazardIdentification} from '../SR_Utils/utils/disclosure-2/disclosure-hazard-identification.generator';
import {generateDisclosureHumanCapitalDevelopment} from '../SR_Utils/utils/disclosure-2/disclosure-human-capital-devt.generator';
import {generateDisclosureHumanRights} from '../SR_Utils/utils/disclosure-2/disclosure-human-rights.generator';
import {generateDisclosureCustomerImpact} from '../SR_Utils/utils/disclosure-2/disclosure-impact-on-customers.generator';
import {generateDisclosureImpactRiskOpportunity} from '../SR_Utils/utils/disclosure-2/disclosure-impact-risk-opportunity.generator';
import {generateDisclosureIndigenousRights} from '../SR_Utils/utils/disclosure-2/disclosure-indigenous-rights.generator';
import {generateDisclosureIndirectEconomicImpact} from '../SR_Utils/utils/disclosure-2/disclosure-indirect-economic-impact.generator';
import {generateDisclosureMaterialTopics} from '../SR_Utils/utils/disclosure-2/disclosure-list-of-material-topics.generator';
import {generateDisclosureLocalCommunities} from '../SR_Utils/utils/disclosure-2/disclosure-local-communities.generator';
import {generateDisclosureManagementMaterialTopics} from '../SR_Utils/utils/disclosure-2/disclosure-management-of-material-topics.generator';
import {generateDisclosureMarketPresence} from '../SR_Utils/utils/disclosure-2/disclosure-market-presence.generator';
import {generateDisclosureMaterialityAssessment} from '../SR_Utils/utils/disclosure-2/disclosure-materiality-assessment.generator';
import {generateDisclosureMaterials} from '../SR_Utils/utils/disclosure-2/disclosure-materials.generator';
import {generateDisclosureMembershipAssociations} from '../SR_Utils/utils/disclosure-2/disclosure-membership-associations.generator';
import {generateDisclosureNonDiscrimination} from '../SR_Utils/utils/disclosure-2/disclosure-non-discrimation.generator ';
import {generateDisclosureHealthServices} from '../SR_Utils/utils/disclosure-2/disclosure-o-health-services.generator';
import {generateDisclosureOHSSafety} from '../SR_Utils/utils/disclosure-2/disclosure-ohs.generator';
import {generateDisclosureOrganizationReportingPractices} from '../SR_Utils/utils/disclosure-2/disclosure-organization-reporting-practices.generator';
import {generateDisclosureParentalLeave} from '../SR_Utils/utils/disclosure-2/disclosure-parental-leave.generator';
import {generateDisclosurePolicyCommitments} from '../SR_Utils/utils/disclosure-2/disclosure-policy-commitments.generator';
import {generateDisclosureGrievanceRedressal} from '../SR_Utils/utils/disclosure-2/disclosure-policy-grievance-redressal.generator';
import {generateDisclosurePoliticalContributions} from '../SR_Utils/utils/disclosure-2/disclosure-political-contributions.generator';
import {generateDisclosurePreventionMitigationOccupationalHealthSafetyImpacts} from '../SR_Utils/utils/disclosure-2/disclosure-prevention-mitigation-occupational-health-safety-impacts.generator';
import {generateDisclosureProcurementPractices} from '../SR_Utils/utils/disclosure-2/disclosure-procurement-practices.generator';
import {generateDisclosureProductServiceQuality} from '../SR_Utils/utils/disclosure-2/disclosure-product-services-quality.generator';
import {generateDisclosurePublicPolicy} from '../SR_Utils/utils/disclosure-2/disclosure-public-policy.generator';
import {generateDisclosureRemuneration} from '../SR_Utils/utils/disclosure-2/disclosure-remuneration.generator';
import {generateDisclosureRisksOpportunitiesClimateChange} from '../SR_Utils/utils/disclosure-2/disclosure-risks-opportunities-climate-change.generator';
import {generateDisclosureRolesResponsibilities} from '../SR_Utils/utils/disclosure-2/disclosure-roles-responsibilities-governance-body.generator';
import {generateDisclosureSecurityPractices} from '../SR_Utils/utils/disclosure-2/disclosure-security-practices.generator';
import {generateDisclosureSocial} from '../SR_Utils/utils/disclosure-2/disclosure-social.generator';
import {generateDisclosureStakeholderEngagement} from '../SR_Utils/utils/disclosure-2/disclosure-stakeholder-engagement.generator';
import {generateDisclosureStrategyPoliciesPractices} from '../SR_Utils/utils/disclosure-2/disclosure-strategy-policies-practices.generator';
import {generateDisclosureSupplierEnvironmental} from '../SR_Utils/utils/disclosure-2/disclosure-supplier-environmental-assessment.generator';
import {generateDisclosureSupplierSocialAssessment} from '../SR_Utils/utils/disclosure-2/disclosure-supplier-social-assessment.generator';
import {generateDisclosureTax} from '../SR_Utils/utils/disclosure-2/disclosure-tax.generator';
import {generateDisclosureTrainingEducation} from '../SR_Utils/utils/disclosure-2/disclosure-training-education.generator';
import {generateDisclosureWasteDirected} from '../SR_Utils/utils/disclosure-2/disclosure-waste-directed-displosal.generator';
import {generateDisclosureWasteDiverted} from '../SR_Utils/utils/disclosure-2/disclosure-waste-diverted-displosal.generator';
import {generateDisclosureWasteGenerated} from '../SR_Utils/utils/disclosure-2/disclosure-waste-generated.generator';
import {generateDisclosureWaste} from '../SR_Utils/utils/disclosure-2/disclosure-waste.generator';
import {generateDisclosureWaterConsumption} from '../SR_Utils/utils/disclosure-2/disclosure-water-consumption.generator';
import {generateDisclosureWaterDischarge} from '../SR_Utils/utils/disclosure-2/disclosure-water-discharge.generator';
import {generateDisclosureWaterEffluents} from '../SR_Utils/utils/disclosure-2/disclosure-water-effluents.generator';
import {generateDisclosureWaterWithdrawal} from '../SR_Utils/utils/disclosure-2/disclosure-water-withdrawals.generator';
import {generateDisclosureWorkRelatedIllHealth} from '../SR_Utils/utils/disclosure-2/disclosure-work-related-ill-health.generator';
import {generateDisclosureWorkRelatedInjuries} from '../SR_Utils/utils/disclosure-2/disclosure-work-related-injuries.generator';
import {generateDisclosureWorkerTraining} from '../SR_Utils/utils/disclosure-2/disclosure-worker-training.generator';

// import {inject} from '@loopback/core';


export class PlatformReportController {
  constructor(@inject(RestBindings.Http.RESPONSE) private response: Response) { }

  async generateDocx(data: any): Promise<Buffer> {
    const doc = new Document({
      styles: {
        default: {
          document: {
            run: {
              font: 'Lato',
              size: 20,
            },
            paragraph: {
              spacing: {after: 120},
            },
          },
        },
      },
      sections: [
        {
          children: [
            new Paragraph({
              text: 'Sustainability Report',
              heading: HeadingLevel.TITLE,
              alignment: AlignmentType.CENTER,
            }),
            new Paragraph({
              text: 'Table of Contents',
              heading: HeadingLevel.HEADING_1,
            }),
            new TableOfContents('Table of Contents', {
              hyperlink: true,
              headingStyleRange: '1-5',
            }),
            new Paragraph({text: '', children: [new PageBreak()]}),

            ...generateDisclosureOrganizationReportingPractices({}),
            ...generateDisclosure2_2({}),
            ...generateDisclosure2_3({}),
            ...generateDisclosure2_4({}),
            ...generateDisclosure2_5({}),
            ...generateDisclosure2_6({}),
            ...generateDisclosure2_7({}),
            ...generateDisclosure2_8({}),
            ...generateDisclosureStakeholderEngagement({}),
            ...generateDisclosureMaterialityAssessment({}),
            ...generateDisclosureMaterialTopics({}),
            ...generateDisclosureManagementMaterialTopics({}),
            ...generateDisclosureGovernanceStructureComposition({}),
            ...generateDisclosureRolesResponsibilities({}),
            ...generateDisclosureConflictsInterest({}),
            ...generateDisclosureRemuneration({}),
            ...generateDisclosureAnnualTotalCompensationRatio({}),
            ...generateDisclosureStrategyPoliciesPractices({}),
            ...generateDisclosurePolicyCommitments({}),
            ...generateDisclosureGrievanceRedressal({}),
            ...generateDisclosureComplianceLawsRegulations({}),
            ...generateDisclosureMembershipAssociations({}),
            ...generateDisclosureCollectiveBargainingAgreements({}),
            ...generateDisclosureEconomicPerformance({}),
            ...generateDisclosureRisksOpportunitiesClimateChange({}),
            ...generateDisclosureMarketPresence({}),
            ...generateDisclosureIndirectEconomicImpact({}),
            ...generateDisclosureProcurementPractices({}),
            ...generateDisclosureSupplierEnvironmental({}),
            ...generateDisclosureSupplierSocialAssessment({}),
            ...generateDisclosureAntiCorruption({}),
            ...generateDisclosureAntiCompetitiveBehavior({}),
            ...generateDisclosureTax({}),
            ...generateDisclosurePublicPolicy({}),
            ...generateDisclosurePoliticalContributions({}),
            ...generateDisclosureMaterials({}),
            ...generateDisclosureEnergy({}),
            ...generateDisclosureEnergyOutside({}),
            ...generateDisclosureEnergyIntensity({}),
            ...generateDisclosureEnergyReduction({}),
            ...generateDisclosureEnergyReductionProducts({}),
            ...generateDisclosureWaterEffluents({}),
            ...generateDisclosureWaterWithdrawal({}),
            ...generateDisclosureWaterDischarge({}),
            ...generateDisclosureWaterConsumption({}),
            ...generateDisclosureBiodiversity({}),
            ...generateDisclosureEmissionsGHG({}),
            ...generateDisclosureGHGIntensity({}),
            ...generateDisclosureGHGReduction({}),
            ...generateDisclosureCarbonPricing({}),
            ...generateDisclosureODS({}),
            ...generateDisclosureAirEmissions({}),
            ...generateDisclosureImpactRiskOpportunity({}),
            ...generateDisclosureWaste({}),
            ...generateDisclosureWasteGenerated({}),
            ...generateDisclosureWasteDiverted({}),
            ...generateDisclosureWasteDirected({}),
            ...generateDisclosureSocial({}),
            ...generateDisclosureBenefitsProvided({}),
            ...generateDisclosureParentalLeave({}),
            ...generateDisclosureOHSSafety({}),
            ...generateDisclosureHazardIdentification({}),
            ...generateDisclosureHealthServices({}),
            ...generateDisclosureWorkerTraining({}),
            ...generateDisclosurePreventionMitigationOccupationalHealthSafetyImpacts(
              {},
            ),
            ...generateDisclosureWorkRelatedInjuries({}),
            ...generateDisclosureWorkRelatedIllHealth({}),
            ...generateDisclosureTrainingEducation({}),
            ...generateDisclosureHumanCapitalDevelopment({}),
            ...generateDisclosureCareerDevelopment({}),
            ...generateDisclosureDiversityInclusion({}),
            ...generateDisclosureBasicSalaryRemuneration({}),
            ...generateDisclosureNonDiscrimination({}),
            ...generateDisclosureHumanRights({}),
            ...generateDisclosureChildLabor({}),
            ...generateDisclosureForcedLabour({}),
            ...generateDisclosureIndigenousRights({}),
            ...generateDisclosureSecurityPractices({}),
            ...generateDisclosureLocalCommunities({}),
            ...generateDisclosureCustomerImpact({}),
            ...generateDisclosureProductServiceQuality({}),
            ...generateDisclosureDataPrivacy({}),
          ],
        },
      ],
    });

    return Packer.toBuffer(doc);
  }

  @post('report/download/word/sr', {
    responses: {
      '200': {
        description: 'Generate Sustainability Report',
        content: {
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            {schema: {type: 'string', format: 'binary'}},
        },
      },
    },
  })
  async generateReport(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              disclosure_2_1: {
                type: 'object',
                properties: {
                  legalName: {type: 'string'},
                  ownershipForm: {type: 'string'},
                  headquarters: {type: 'string'},
                  countriesOfOperation: {type: 'string'},
                },
              },
            },
          },
        },
      },
    })
    data: any,
  ): Promise<void> {
    try {
      const buffer = await this.generateDocx(data);
      const fileName = 'Sustainability_Report.docx';
      this.response.setHeader(
        'Content-Disposition',
        `attachment; filename=${fileName}`,
      );
      this.response.contentType(
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      );
      this.response.send(buffer);
    } catch (err: any) {
      this.response.status(500).json({error: err.message});
    }
  }

  @post('/report/download/pdf/sr')
  async downloadPDF(
    @inject(RestBindings.Http.RESPONSE) response: Response,
    @requestBody() body: {html: string},
  ): Promise<Response> {
    const html = body.html;

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();
    await page.setContent(html, {waitUntil: 'networkidle0'});

    const filePath = path.join(__dirname, '../../files/gri.pdf');
    await page.pdf({path: filePath, format: 'A4', printBackground: true});
    await browser.close();

    response.setHeader('Content-Type', 'application/pdf');
    response.setHeader(
      'Content-Disposition',
      'attachment; filename="GRI_Report.pdf"',
    );
    response.download(filePath);
    return response;
  }
}
