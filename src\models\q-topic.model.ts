import {Entity, model, property, hasMany} from '@loopback/repository';
import {QSection} from './q-section.model';

@model()
export class QTopic extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  qCategoryId?: number;

  @hasMany(() => QSection)
  qSections: QSection[];

  constructor(data?: Partial<QTopic>) {
    super(data);
  }
}

export interface QTopicRelations {
  // describe navigational properties here
}

export type QTopicWithRelations = QTopic & QTopicRelations;
