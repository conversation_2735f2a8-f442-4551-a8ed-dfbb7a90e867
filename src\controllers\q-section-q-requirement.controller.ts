import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  QSection,
  QRequirement,
} from '../models';
import {QSectionRepository} from '../repositories';

export class QSectionQRequirementController {
  constructor(
    @repository(QSectionRepository) protected qSectionRepository: QSectionRepository,
  ) { }

  @get('/q-sections/{id}/q-requirements', {
    responses: {
      '200': {
        description: 'Array of QSection has many QRequirement',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QRequirement)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QRequirement>,
  ): Promise<QRequirement[]> {
    return this.qSectionRepository.qRequirements(id).find(filter);
  }

  @post('/q-sections/{id}/q-requirements', {
    responses: {
      '200': {
        description: 'QSection model instance',
        content: {'application/json': {schema: getModelSchemaRef(QRequirement)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof QSection.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QRequirement, {
            title: 'NewQRequirementInQSection',
            exclude: ['id'],
            optional: ['qSectionId']
          }),
        },
      },
    }) qRequirement: Omit<QRequirement, 'id'>,
  ): Promise<QRequirement> {
    return this.qSectionRepository.qRequirements(id).create(qRequirement);
  }

  @patch('/q-sections/{id}/q-requirements', {
    responses: {
      '200': {
        description: 'QSection.QRequirement PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QRequirement, {partial: true}),
        },
      },
    })
    qRequirement: Partial<QRequirement>,
    @param.query.object('where', getWhereSchemaFor(QRequirement)) where?: Where<QRequirement>,
  ): Promise<Count> {
    return this.qSectionRepository.qRequirements(id).patch(qRequirement, where);
  }

  @del('/q-sections/{id}/q-requirements', {
    responses: {
      '200': {
        description: 'QSection.QRequirement DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(QRequirement)) where?: Where<QRequirement>,
  ): Promise<Count> {
    return this.qSectionRepository.qRequirements(id).delete(where);
  }
}
