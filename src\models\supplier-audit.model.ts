import {Entity, model, property} from '@loopback/repository';

@model()
export class SupplierAudit extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'any',
    mysql: {
      dataType: 'LONGTEXT',
    },
  })
  selfAuditResponse?: any;

  @property({
    type: 'any',
    mysql: {
      dataType: 'LONGTEXT',
    },
  })
  actualAuditResponse?: any;

  @property({
    type: 'any',
    mysql: {
      dataType: 'LONGTEXT',
    },
  })
  response?: any;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'string',
  })
  submitted_on?: string;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  supplierMSIScore?: number;

  @property({
    type: 'number',
  })
  status?: number;

  @property({
    type: 'string',
  })
  vendorCode?: string;

  @property({
    type: 'number',
  })
  vendorId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'string',
  })
  typeOfAudit?: string;

  constructor(data?: Partial<SupplierAudit>) {
    super(data);
  }
}

export interface SupplierAuditRelations {
  // describe navigational properties here
}

export type SupplierAuditWithRelations = SupplierAudit & SupplierAuditRelations;
