import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SupplierAudit} from '../models';
import {SupplierAuditRepository} from '../repositories';

export class SupplierAuditController {
  constructor(
    @repository(SupplierAuditRepository)
    public supplierAuditRepository : SupplierAuditRepository,
  ) {}

  @post('/supplier-audits')
  @response(200, {
    description: 'SupplierAudit model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierAudit)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAudit, {
            title: 'NewSupplierAudit',
            exclude: ['id'],
          }),
        },
      },
    })
    supplierAudit: Omit<SupplierAudit, 'id'>,
  ): Promise<SupplierAudit> {
    return this.supplierAuditRepository.create(supplierAudit);
  }

  @get('/supplier-audits/count')
  @response(200, {
    description: 'SupplierAudit model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplierAudit) where?: Where<SupplierAudit>,
  ): Promise<Count> {
    return this.supplierAuditRepository.count(where);
  }

  @get('/supplier-audits')
  @response(200, {
    description: 'Array of SupplierAudit model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplierAudit, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplierAudit) filter?: Filter<SupplierAudit>,
  ): Promise<SupplierAudit[]> {
    return this.supplierAuditRepository.find(filter);
  }

  @patch('/supplier-audits')
  @response(200, {
    description: 'SupplierAudit PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAudit, {partial: true}),
        },
      },
    })
    supplierAudit: SupplierAudit,
    @param.where(SupplierAudit) where?: Where<SupplierAudit>,
  ): Promise<Count> {
    return this.supplierAuditRepository.updateAll(supplierAudit, where);
  }

  @get('/supplier-audits/{id}')
  @response(200, {
    description: 'SupplierAudit model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplierAudit, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplierAudit, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplierAudit>
  ): Promise<SupplierAudit> {
    return this.supplierAuditRepository.findById(id, filter);
  }

  @patch('/supplier-audits/{id}')
  @response(204, {
    description: 'SupplierAudit PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAudit, {partial: true}),
        },
      },
    })
    supplierAudit: SupplierAudit,
  ): Promise<void> {
    await this.supplierAuditRepository.updateById(id, supplierAudit);
  }

  @put('/supplier-audits/{id}')
  @response(204, {
    description: 'SupplierAudit PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplierAudit: SupplierAudit,
  ): Promise<void> {
    await this.supplierAuditRepository.replaceById(id, supplierAudit);
  }

  @del('/supplier-audits/{id}')
  @response(204, {
    description: 'SupplierAudit DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplierAuditRepository.deleteById(id);
  }
}
