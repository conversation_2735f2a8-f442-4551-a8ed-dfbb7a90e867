import {Entity, model, property, hasMany} from '@loopback/repository';
import {SupplyChecklist} from './supply-checklist.model';

@model()
export class SupplySection extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @property({
    type: 'string',
  })
  updatedAt?: string;

  @property({
    type: 'boolean',
  })
  status?: boolean;

  @property({
    type: 'number',
  })
  supplyCategoryId?: number;

  @hasMany(() => SupplyChecklist)
  supplyChecklists: SupplyChecklist[];

  constructor(data?: Partial<SupplySection>) {
    super(data);
  }
}

export interface SupplySectionRelations {
  // describe navigational properties here
}

export type SupplySectionWithRelations = SupplySection & SupplySectionRelations;
