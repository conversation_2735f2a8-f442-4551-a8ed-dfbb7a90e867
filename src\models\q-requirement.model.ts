import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class QRequirement extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  question?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  type?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  corporateOrCollatedInfo?: string[];

  @property({
    type: 'string',
  })
  supportingDocumentation?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  framework?: object[];

  @property({
    type: 'number',
  })
  qSectionId?: number;

  @property({
    type: 'string',
  })
  created?: string;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<QRequirement>) {
    super(data);
  }
}

export interface QRequirementRelations {
  // describe navigational properties here
}

export type QRequirementWithRelations = QRequirement & QRequirementRelations;
