import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {QSection} from '../models';
import {QSectionRepository} from '../repositories';

export class QSectionController {
  constructor(
    @repository(QSectionRepository)
    public qSectionRepository : QSectionRepository,
  ) {}

  @post('/q-sections')
  @response(200, {
    description: 'QSection model instance',
    content: {'application/json': {schema: getModelSchemaRef(QSection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QSection, {
            title: 'NewQSection',
            exclude: ['id'],
          }),
        },
      },
    })
    qSection: Omit<QSection, 'id'>,
  ): Promise<QSection> {
    return this.qSectionRepository.create(qSection);
  }

  @get('/q-sections/count')
  @response(200, {
    description: 'QSection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QSection) where?: Where<QSection>,
  ): Promise<Count> {
    return this.qSectionRepository.count(where);
  }

  @get('/q-sections')
  @response(200, {
    description: 'Array of QSection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QSection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QSection) filter?: Filter<QSection>,
  ): Promise<QSection[]> {
    return this.qSectionRepository.find(filter);
  }

  @patch('/q-sections')
  @response(200, {
    description: 'QSection PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QSection, {partial: true}),
        },
      },
    })
    qSection: QSection,
    @param.where(QSection) where?: Where<QSection>,
  ): Promise<Count> {
    return this.qSectionRepository.updateAll(qSection, where);
  }

  @get('/q-sections/{id}')
  @response(200, {
    description: 'QSection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QSection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QSection, {exclude: 'where'}) filter?: FilterExcludingWhere<QSection>
  ): Promise<QSection> {
    return this.qSectionRepository.findById(id, filter);
  }

  @patch('/q-sections/{id}')
  @response(204, {
    description: 'QSection PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QSection, {partial: true}),
        },
      },
    })
    qSection: QSection,
  ): Promise<void> {
    await this.qSectionRepository.updateById(id, qSection);
  }

  @put('/q-sections/{id}')
  @response(204, {
    description: 'QSection PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() qSection: QSection,
  ): Promise<void> {
    await this.qSectionRepository.replaceById(id, qSection);
  }

  @del('/q-sections/{id}')
  @response(204, {
    description: 'QSection DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.qSectionRepository.deleteById(id);
  }
}
