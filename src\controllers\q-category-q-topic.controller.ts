import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  QCategory,
  QTopic,
} from '../models';
import {QCategoryRepository} from '../repositories';

export class QCategoryQTopicController {
  constructor(
    @repository(QCategoryRepository) protected qCategoryRepository: QCategoryRepository,
  ) { }

  @get('/q-categories/{id}/q-topics', {
    responses: {
      '200': {
        description: 'Array of QCategory has many QTopic',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QTopic)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QTopic>,
  ): Promise<QTopic[]> {
    return this.qCategoryRepository.qTopics(id).find(filter);
  }

  @post('/q-categories/{id}/q-topics', {
    responses: {
      '200': {
        description: 'QCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(QTopic)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof QCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QTopic, {
            title: 'NewQTopicInQCategory',
            exclude: ['id'],
            optional: ['qCategoryId']
          }),
        },
      },
    }) qTopic: Omit<QTopic, 'id'>,
  ): Promise<QTopic> {
    return this.qCategoryRepository.qTopics(id).create(qTopic);
  }

  @patch('/q-categories/{id}/q-topics', {
    responses: {
      '200': {
        description: 'QCategory.QTopic PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QTopic, {partial: true}),
        },
      },
    })
    qTopic: Partial<QTopic>,
    @param.query.object('where', getWhereSchemaFor(QTopic)) where?: Where<QTopic>,
  ): Promise<Count> {
    return this.qCategoryRepository.qTopics(id).patch(qTopic, where);
  }

  @del('/q-categories/{id}/q-topics', {
    responses: {
      '200': {
        description: 'QCategory.QTopic DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(QTopic)) where?: Where<QTopic>,
  ): Promise<Count> {
    return this.qCategoryRepository.qTopics(id).delete(where);
  }
}
