import {Entity, model, property, hasMany, belongsTo} from '@loopback/repository';
import {QRequirement} from './q-requirement.model';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';

@model()
export class QSection extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  qTopicId?: number;

  @hasMany(() => QRequirement)
  qRequirements: QRequirement[];

  @belongsTo(() => ConsolidateFormCollection)
  srfId: number;

  constructor(data?: Partial<QSection>) {
    super(data);
  }
}

export interface QSectionRelations {
  // describe navigational properties here
}

export type QSectionWithRelations = QSection & QSectionRelations;
