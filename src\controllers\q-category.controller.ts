import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {QCategory} from '../models';
import {QCategoryRepository} from '../repositories';

export class QCategoryController {
  constructor(
    @repository(QCategoryRepository)
    public qCategoryRepository: QCategoryRepository,
  ) { }

  @post('/q-categories')
  @response(200, {
    description: 'QCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(QCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QCategory, {
            title: 'NewQCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    qCategory: Omit<QCategory, 'id'>,
  ): Promise<QCategory> {
    return this.qCategoryRepository.create(qCategory);
  }

  @get('/q-categories/count')
  @response(200, {
    description: 'QCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QCategory) where?: Where<QCategory>,
  ): Promise<Count> {
    return this.qCategoryRepository.count(where);
  }

  @get('/q-categories')
  @response(200, {
    description: 'Array of QCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QCategory) filter?: Filter<QCategory>,
  ): Promise<QCategory[]> {
    return this.qCategoryRepository.find(filter);
  }

  @get('/q-categories-all')
  @response(200, {
    description: 'Array of QCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QCategory, {includeRelations: true}),
        },
      },
    },
  })
  async findAll(
    @param.filter(QCategory) filter?: Filter<QCategory>,
  ): Promise<QCategory[]> {
    return this.qCategoryRepository.find({include: [{relation: 'qTopics', scope: {include: [{relation: 'qSections'}]}}]});

  }

  @patch('/q-categories')
  @response(200, {
    description: 'QCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QCategory, {partial: true}),
        },
      },
    })
    qCategory: QCategory,
    @param.where(QCategory) where?: Where<QCategory>,
  ): Promise<Count> {
    return this.qCategoryRepository.updateAll(qCategory, where);
  }

  @get('/q-categories/{id}')
  @response(200, {
    description: 'QCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<QCategory>
  ): Promise<QCategory> {
    return this.qCategoryRepository.findById(id, filter);
  }

  @patch('/q-categories/{id}')
  @response(204, {
    description: 'QCategory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QCategory, {partial: true}),
        },
      },
    })
    qCategory: QCategory,
  ): Promise<void> {
    await this.qCategoryRepository.updateById(id, qCategory);
  }

  @put('/q-categories/{id}')
  @response(204, {
    description: 'QCategory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() qCategory: QCategory,
  ): Promise<void> {
    await this.qCategoryRepository.replaceById(id, qCategory);
  }

  @del('/q-categories/{id}')
  @response(204, {
    description: 'QCategory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.qCategoryRepository.deleteById(id);
  }
}
